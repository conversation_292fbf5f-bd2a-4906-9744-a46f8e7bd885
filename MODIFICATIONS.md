# Standard Modifications for Brizy Exports

This document contains the standard modifications that need to be applied after each Brizy export. These modifications address technical limitations of the Brizy platform and remain consistent across different exports.

## Project Configuration

**IMPORTANT**: Update these values for your specific project before applying modifications.

```
PROJECT_DOMAIN = "www.cymta.org"  # Replace with your actual domain (no https:// or trailing slash)
DEFAULT_LANGUAGE = "el"         # Primary language code (e.g., "en", "fr", "de")
ADDITIONAL_LANGUAGES = ["en"]   # Additional language codes as an array (e.g., ["el", "fr", "es"])
```

## Lessons Learned for Brizy Sites

### File Editing Best Practices

-   **NEVER use str-replace-editor on Brizy HTML files** - The IDE auto-formatting will break the single-line structure
-   **Use command-line tools (sed) instead** for HTML modifications to preserve Brizy's formatting
-   **Test one file first** before applying bulk changes to ensure the sed commands work correctly
-   **Handle terminal timeouts gracefully** - commands may appear to hang but complete successfully

### Asset Path Patterns

-   **One level deep** (e.g., `contact/index.html`): Use `../assets/` for all asset references
-   **Two levels deep** (e.g., `en/contact/index.html`): Use `../../assets/` for all asset references
-   **Common asset reference patterns to update**:
    -   `href="assets/` → `href="../assets/` (or `../../assets/`)
    -   `src="assets/` → `src="../assets/` (or `../../assets/`)
    -   `srcset="assets/` → `srcset="../assets/` (or `../../assets/`)
    -   `url("assets/` → `url("../assets/` (or `../../assets/`)
    -   Catch-all: `assets/` → `../assets/` (or `../../assets/`)

### Canonical Tag Patterns

-   **Homepage**: `href=""` → `href="https://domain.com/"`
-   **Default language pages**: `href="/page"` → `href="https://domain.com/page/"`
-   **Additional language pages**: `href="/page-en"` → `href="https://domain.com/en/page/"`

### Bulk Operations Strategy

-   **Process files in batches** by directory depth to avoid path confusion
-   **Use loops in shell commands** for efficiency: `for dir in list; do commands; done`
-   **Always verify changes** on a sample file before applying to all files
-   **Fix inconsistent paths** that may result from multiple sed operations

### Git Workflow

-   **Commit after each step**: Make a Git commit immediately after completing each modification step
-   **Use step titles as commit messages**: Use the exact modification title (e.g., "Restructure page organization")
-   **Sequential commits**: This creates a clear history of what was applied and when
-   **Easy rollback**: Individual commits allow rolling back specific modifications if needed

## Modification Checklist

Use this checklist to track your progress as you apply modifications:

-   [x] [Restructure page organization (folder structure)](#modification-restructure-page-organization)
-   [x] [Fix asset paths after restructuring](#modification-fix-asset-paths-after-restructuring)
-   [x] [Fix internal links (remove .html extensions)](#modification-fix-internal-links-remove-html-extensions)
-   [x] [Add canonical tags](#modification-add-canonical-tags)
-   [x] [Add language tags (hreflang)](#modification-add-hreflang-tags-for-multilingual-seo)
-   [x] [Create sitemap.xml](#modification-create-sitemapxml)
-   [x] [Create robots.txt](#modification-create-robotstxt)
-   [x] [Replace form submission URLs](#modification-replace-form-submission-urls)
-   [x] [Fix navigation links and English homepage structure](#critical-navigation-link-issues)
-   [ ] _(Future modifications will be added here)_

## Important Reminders

**ALWAYS REMEMBER:**

1. **Check off completed tasks** in this checklist as you finish each modification
2. **No trailing slashes in canonical URLs** (except homepage which should have trailing slash)
    - ✅ Correct: `https://www.cymta.org/contact`
    - ❌ Wrong: `https://www.cymta.org/contact/`
    - ✅ Homepage exception: `https://www.cymta.org/` (with trailing slash)

## Critical Navigation Link Issues

### The `/home` and `/en/home` Problem

**Problem**: Brizy may generate navigation links pointing to `/home` or place English content at `/en/home/<USER>'t work properly in self-hosted environments and create non-standard URL structures.

**Symptoms**:

-   Navigation links pointing to `href="/home"` that lead to 404 errors
-   English homepage located at `/en/home/<USER>/en/`
-   Broken navigation when self-hosting (works in Brizy preview but fails in production)

**Root Cause**: Brizy's internal routing system handles these redirects during preview, but they don't exist in the exported static files.

**Solution Strategy**:

1. **Fix all `/home` navigation links**: Replace `href="/home"` with `href="/"` to point to actual homepage
2. **Move English homepage**: Relocate from `/en/home/<USER>/en/index.html` for cleaner URLs
3. **Update all references**: Fix asset paths, canonical URLs, hreflang tags, and sitemap entries

**Critical Steps When Moving English Homepage**:

-   ✅ Move file from `/en/home/<USER>/en/index.html`
-   ✅ Update asset paths from `../../assets/` to `../assets/` (one level up instead of two)
-   ✅ Update canonical URL from `/en/home` to `/en`
-   ✅ Update hreflang tags from `/en/home` to `/en`
-   ✅ Update og:url meta tag from `/home-en` to `/en`
-   ✅ Update sitemap.xml entries to reflect new URL structure
-   ✅ Use command-line tools (sed) to preserve minified HTML format

**Important Lessons Learned**:

-   **Always use sed for Brizy modifications** to avoid auto-formatting that breaks minified HTML
-   **Test navigation thoroughly** after any structural changes
-   **Check all meta tags** (canonical, hreflang, og:url) when changing URL structure
-   **Update sitemap.xml** whenever URL structure changes
-   **Verify asset paths** match the new directory depth (one `../` per level up)

## Modifications

### Modification: Restructure Page Organization

**Purpose**: Improve SEO and URL structure by organizing pages into directories instead of using Brizy's flat file structure

**Current Brizy Structure**:

```
root/
├── index.html
├── my-page1.html
├── my-page1-el.html (Greek version)
├── my-page2.html
└── my-page2-el.html (Greek version)
```

**Desired Structure**:

```
root/
├── index.html
├── my-page1/
│   └── index.html
├── my-page2/
│   └── index.html
└── el/
    ├── my-page1/
    │   └── index.html
    └── my-page2/
    │   └── index.html
```

**Process**:

1. Create appropriate directories for each page
2. Move each HTML file to its corresponding directory and rename to index.html
3. Create language subdirectories (e.g., "el" for Greek) and organize translated pages within them

**Example**:

-   Move `my-page1.html` to `my-page1/index.html`
-   Move `my-page1-el.html` to `el/my-page1/index.html`

**Note**: The downloaded files already contain the correct internal links for the new URL structure, so no link updates are needed. After restructuring, test all navigation links to ensure they work correctly with the new folder organization.

### Modification: Fix Asset Paths After Restructuring

**Purpose**: Update relative paths to CSS, JavaScript, and image files so they work correctly after moving HTML files into subdirectories

**Problem**: When HTML files are moved from the root directory to subdirectories (e.g., `page1.html` → `page1/index.html`), the relative paths to the `assets/` folder break because the files are now one level deeper in the directory structure.

**What Breaks**:

-   CSS files: `assets/filename.css` becomes unreachable
-   JavaScript files: `assets/filename.js` becomes unreachable
-   Images: `assets/img/filename.jpg` becomes unreachable
-   SVG files: `assets/svg/filename.svg` becomes unreachable

**Location**: In all HTML files that have been moved to subdirectories (not the root `index.html`)

**What to Do**:

1. **Identify affected files**: Only HTML files moved to subdirectories need path updates
2. **Update asset paths**: Add `../` prefix to all `assets/` references
3. **Test thoroughly**: Verify all CSS, JavaScript, and images load correctly

**Examples of Required Changes**:

For files moved to subdirectories like `page1/index.html`, `el/page1/index.html`, etc.:

```html
<!-- BEFORE (broken after restructuring) -->
<link href="assets/2e9ffc8d836259bac3e2a55f7ffd6f65.css" rel="stylesheet" />
<script src="assets/9a938adb5391cc513c01ebb0af8e305e.js"></script>
background-image: url("assets/img/filename.jpg"); -webkit-mask-image:
url("assets/svg/filename.svg");

<!-- AFTER (fixed paths) -->
<link href="../assets/2e9ffc8d836259bac3e2a55f7ffd6f65.css" rel="stylesheet" />
<script src="../assets/9a938adb5391cc513c01ebb0af8e305e.js"></script>
background-image: url("../assets/img/filename.jpg"); -webkit-mask-image:
url("../assets/svg/filename.svg");
```

For files moved to language subdirectories like `el/page1/index.html`, you need `../../` (two levels up):

```html
<!-- BEFORE (broken after restructuring) -->
<link href="assets/2e9ffc8d836259bac3e2a55f7ffd6f65.css" rel="stylesheet" />

<!-- AFTER (fixed paths for language subdirectories) -->
<link
    href="../../assets/2e9ffc8d836259bac3e2a55f7ffd6f65.css"
    rel="stylesheet"
/>
```

**Important Notes**:

-   **Root index.html**: Do NOT change asset paths in the root `index.html` file - these should remain as `assets/`
-   **One level deep**: For pages like `page1/index.html`, use `../assets/`
-   **Two levels deep**: For language pages like `el/page1/index.html`, use `../../assets/`
-   **All asset types**: Update paths for CSS files, JavaScript files, images, SVGs, and any other assets
-   **CSS content**: Also check for asset references inside CSS `<style>` blocks and update those paths
-   **Test immediately**: After making changes, open the pages in a browser to verify all assets load correctly

**Common Asset Reference Patterns to Find and Update**:

-   `href="assets/` → `href="../assets/` (or `../../assets/` for language subdirectories)
-   `src="assets/` → `src="../assets/` (or `../../assets/` for language subdirectories)
-   `url("assets/` → `url("../assets/` (or `url("../../assets/` for language subdirectories)
-   `background-image: url("assets/` → `background-image: url("../assets/`
-   `-webkit-mask-image: url("assets/` → `-webkit-mask-image: url("../assets/`

### Modification: Fix Internal Links (Remove .html Extensions)

**Purpose**: Remove incorrect `.html` extensions from internal navigation links to ensure clean URLs that match the new folder structure

**Problem**: Brizy exports sometimes generate internal links with `.html` extensions (e.g., `/find.html`, `/contact.html`) which don't work with the new directory structure where pages are organized as `/find/`, `/contact/`, etc.

**What Breaks**:

-   Navigation links like `href="/find.html"` lead to 404 errors
-   Inconsistent URL structure between navigation and actual page locations
-   Poor SEO due to broken internal links

**Location**: In all HTML files, typically in navigation menus, buttons, and internal links

**What to Do**:

1. **Search all HTML files** for internal links with `.html` extensions
2. **Remove the `.html` extension** from internal links
3. **Ensure links point to the correct directory structure**
4. **Test all navigation** to verify links work correctly

**Examples of Required Changes**:

```html
<!-- BEFORE (incorrect with .html extension) -->
<a href="/find.html">Find Us</a>
<a href="/contact.html">Contact</a>
<a href="/about.html">About</a>
<a href="/services.html">Services</a>

<!-- AFTER (correct without .html extension) -->
<a href="/find">Find Us</a>
<a href="/contact">Contact</a>
<a href="/about">About</a>
<a href="/services">Services</a>
```

For language-specific links:

```html
<!-- BEFORE (incorrect) -->
<a href="/find-el.html">Βρείτε μας</a>
<a href="/contact-el.html">Επικοινωνία</a>

<!-- AFTER (correct) -->
<a href="/el/find">Βρείτε μας</a>
<a href="/el/contact">Επικοινωνία</a>
```

**Important Notes**:

-   **Root homepage**: Links to `/` or `/index.html` should become just `/`
-   **Internal pages**: Remove `.html` and ensure the path matches the directory structure
-   **Language pages**: Update both the extension removal and the language prefix structure
-   **External links**: Do NOT modify external links (those starting with `http://` or `https://`)
-   **Asset links**: Do NOT modify links to CSS, JS, or image files in the `assets/` folder
-   **Anchor links**: Preserve anchor links (e.g., `#section`) when removing `.html`

**Common Link Patterns to Find and Fix**:

-   `href="/page-name.html"` → `href="/page-name"`
-   `href="/page-name-el.html"` → `href="/el/page-name"`
-   `href="/page-name-en.html"` → `href="/en/page-name"`
-   `href="page-name.html"` → `href="/page-name"` (add leading slash for consistency)
-   `href="index.html"` → `href="/"`

**Testing Strategy**:

1. **Use browser developer tools** to check for 404 errors in the Network tab
2. **Click through all navigation links** to ensure they work
3. **Check both desktop and mobile navigation** menus
4. **Verify language switching** links work correctly
5. **Test any buttons or CTAs** that link to internal pages

### Modification: Add Canonical Tags

**Purpose**: Ensure all pages have proper canonical tags to prevent duplicate content issues and improve SEO

**Problem**: Brizy exports often have missing or incorrect canonical tags that may point to old URLs (like "page-en.html" or "page-fr.html")

**Location**: In the `<head>` section of each HTML file

**What to Do**:

1. **Check each page** for existing canonical tags
2. **Add canonical tags** if they don't exist
3. **Correct canonical tags** if they point to old URLs or incorrect locations
4. **Ensure canonical URLs** match the new folder structure after restructuring

**Examples**:

For a page at `my-page1/index.html`:

```html
<head>
    <!-- Existing head content -->

    <!-- Add this canonical tag (NO trailing slash) -->
    <link rel="canonical" href="https://PROJECT_DOMAIN/my-page1" />
</head>
```

For a language-specific page at `el/my-page1/index.html`:

```html
<head>
    <!-- Existing head content -->

    <!-- Add this canonical tag (NO trailing slash) -->
    <link rel="canonical" href="https://PROJECT_DOMAIN/el/my-page1" />
</head>
```

**Important Notes**:

-   Replace `PROJECT_DOMAIN` with the actual domain from the Project Configuration section
-   Canonical tags should point to the current page's URL (not the default language version)
-   Every page should have exactly one canonical tag
-   Remove any incorrect existing canonical tags before adding the correct one

### Modification: Add hreflang Tags for Multilingual SEO

**Purpose**: Inform search engines about language alternatives for each page to improve multilingual SEO and prevent duplicate content issues

**Location**: In the `<head>` section of each HTML file

**What to Add**:
For each page, add `<link>` elements with `hreflang` attributes that point to all language versions of that page.

**Example for Default Language page** (`my-page1/index.html`):

```html
<head>
    <!-- Existing head content -->

    <!-- Language annotations -->
    <link
        rel="alternate"
        hreflang="DEFAULT_LANGUAGE"
        href="https://PROJECT_DOMAIN/my-page1/"
    />
    <link
        rel="alternate"
        hreflang="x-default"
        href="https://PROJECT_DOMAIN/my-page1/"
    />
    <link
        rel="alternate"
        hreflang="ADDITIONAL_LANGUAGE"
        href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/my-page1/"
    />
</head>
```

**Example for Additional Language page** (`el/my-page1/index.html`):

```html
<head>
    <!-- Existing head content -->

    <!-- Language annotations -->
    <link
        rel="alternate"
        hreflang="DEFAULT_LANGUAGE"
        href="https://PROJECT_DOMAIN/my-page1/"
    />
    <link
        rel="alternate"
        hreflang="ADDITIONAL_LANGUAGE"
        href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/my-page1/"
    />
</head>
```

**Important Notes**:

-   Replace the placeholder variables (`PROJECT_DOMAIN`, `DEFAULT_LANGUAGE`, `ADDITIONAL_LANGUAGE`) with the values from the Project Configuration section
-   Include all language versions in each page's `<head>` section
-   For the default language, also add the `hreflang="x-default"` link
-   If you have multiple additional languages, add a link element for each one
-   This approach follows Google's recommendations for multilingual sites
-   For pages without translations, skip the hreflang tags (canonical tags are handled in the previous step)

### Modification: Create sitemap.xml

**Purpose**: Create a complete sitemap that includes all pages for better search engine indexing and SEO

**Problem**: Brizy exports don't include a proper sitemap.xml file with all pages and language versions

**Location**: Root directory `sitemap.xml` file (create new file)

**What to Do**:

1. **Create a new file** called `sitemap.xml` in the root directory
2. **Include all pages** that exist after folder restructuring
3. **Include all language versions** of each page
4. **Add hreflang annotations** for multilingual SEO
5. **Set appropriate priority and changefreq values**

**Complete Sitemap Template with hreflang**:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">

    <!-- Homepage - Default Language -->
    <url>
        <loc>https://PROJECT_DOMAIN/</loc>
        <xhtml:link rel="alternate" hreflang="DEFAULT_LANGUAGE" href="https://PROJECT_DOMAIN/" />
        <xhtml:link rel="alternate" hreflang="ADDITIONAL_LANGUAGE" href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/" />
        <xhtml:link rel="alternate" hreflang="x-default" href="https://PROJECT_DOMAIN/" />
        <lastmod>2024-01-01</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>

    <!-- Homepage - Additional Language -->
    <url>
        <loc>https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/</loc>
        <xhtml:link rel="alternate" hreflang="DEFAULT_LANGUAGE" href="https://PROJECT_DOMAIN/" />
        <xhtml:link rel="alternate" hreflang="ADDITIONAL_LANGUAGE" href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/" />
        <xhtml:link rel="alternate" hreflang="x-default" href="https://PROJECT_DOMAIN/" />
        <lastmod>2024-01-01</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>

    <!-- Page1 - Default Language -->
    <url>
        <loc>https://PROJECT_DOMAIN/page1/</loc>
        <xhtml:link rel="alternate" hreflang="DEFAULT_LANGUAGE" href="https://PROJECT_DOMAIN/page1/" />
        <xhtml:link rel="alternate" hreflang="ADDITIONAL_LANGUAGE" href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page1/" />
        <xhtml:link rel="alternate" hreflang="x-default" href="https://PROJECT_DOMAIN/page1/" />
        <lastmod>2024-01-01</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>

    <!-- Page1 - Additional Language -->
    <url>
        <loc>https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page1/</loc>
        <xhtml:link rel="alternate" hreflang="DEFAULT_LANGUAGE" href="https://PROJECT_DOMAIN/page1/" />
        <xhtml:link rel="alternate" hreflang="ADDITIONAL_LANGUAGE" href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page1/" />
        <xhtml:link rel="alternate" hreflang="x-default" href="https://PROJECT_DOMAIN/page1/" />
        <lastmod>2024-01-01</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>

    <!-- Page2 - Default Language -->
    <url>
        <loc>https://PROJECT_DOMAIN/page2/</loc>
        <xhtml:link rel="alternate" hreflang="DEFAULT_LANGUAGE" href="https://PROJECT_DOMAIN/page2/" />
        <xhtml:link rel="alternate" hreflang="ADDITIONAL_LANGUAGE" href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page2/" />
        <xhtml:link rel="alternate" hreflang="x-default" href="https://PROJECT_DOMAIN/page2/" />
        <lastmod>2024-01-01</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>

    <!-- Page2 - Additional Language -->
    <url>
        <loc>https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page2/</loc>
        <xhtml:link rel="alternate" hreflang="DEFAULT_LANGUAGE" href="https://PROJECT_DOMAIN/page2/" />
        <xhtml:link rel="alternate" hreflang="ADDITIONAL_LANGUAGE" href="https://PROJECT_DOMAIN/ADDITIONAL_LANGUAGE/page2/" />
        <xhtml:link rel="alternate" hreflang="x-default" href="https://PROJECT_DOMAIN/page2/" />
        <lastmod>2024-01-01</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
</urlset>
```

**Important Notes**:

-   Replace `PROJECT_DOMAIN`, `DEFAULT_LANGUAGE`, and `ADDITIONAL_LANGUAGE` with values from the Project Configuration section
-   Replace `page1`, `page2`, etc. with your actual page names
-   Add entries for ALL pages that exist after restructuring
-   Include both default language and translated versions as separate `<url>` entries
-   Each page entry must include hreflang links to ALL language versions of that page
-   The `x-default` hreflang should always point to the default language version
-   Update the `lastmod` date to the current date when updating the sitemap
-   Homepage should have `priority="1.0"`, other pages typically `priority="0.8"`
-   Use `changefreq="weekly"` for homepage, `changefreq="monthly"` for other pages
-   Make sure URLs end with `/` to match the folder structure
-   Include the `xmlns:xhtml` namespace declaration for hreflang support
-   For pages without translations, omit the hreflang links and use a simple `<url>` entry

**Steps to Create**:

1. Identify all HTML files after restructuring (both default and language-specific)
2. Create a URL entry for each page using the new folder structure
3. Create the `sitemap.xml` file in the root directory with the complete sitemap content
4. Verify all URLs are correct and accessible

### Modification: Create robots.txt

**Purpose**: Create a robots.txt file that allows all crawlers and points to the correct sitemap

**Problem**: Brizy exports don't include a robots.txt file, which is needed for proper SEO

**Location**: Root directory `robots.txt` file (create new file)

**What to Do**:

1. **Create a new file** called `robots.txt` in the root directory
2. **Add the following content** (replace PROJECT_DOMAIN with actual domain):

```
User-agent: *
Allow: /

Sitemap: https://PROJECT_DOMAIN/sitemap.xml
```

**Complete Example**:

If your PROJECT_DOMAIN is `mywebsite.com`, create `robots.txt` with:

```
User-agent: *
Allow: /

Sitemap: https://mywebsite.com/sitemap.xml
```

**Important Notes**:

-   Replace `PROJECT_DOMAIN` with the actual domain from the Project Configuration section
-   Include `https://` in the sitemap URL (e.g., `https://mywebsite.com/sitemap.xml`)
-   Make sure the domain matches exactly what you used in canonical tags and sitemap
-   This robots.txt allows all crawlers since these are content websites
-   The file should be placed in the root directory alongside index.html

### Modification: Replace Form Submission URLs

**Purpose**: Replace Brizy's default form submission URLs with custom form handling endpoints

**Problem**: Brizy exports often contain default form submission URLs (like `https://msg.formsender.online/form/submit`) that need to be replaced with custom endpoints

**Location**: In HTML files that contain forms (usually in `action` attributes of `<form>` elements)

**What to Do**:

1. **Search all HTML files** for form submission URLs that match patterns like:

    - `https://msg.formsender.online/form/submit`
    - `https://msg.formsender.online/` (any variation)
    - Other similar form service URLs

2. **Ask the user for confirmation** before making any changes:

    - Show the user which files contain forms
    - Show the current form URLs found
    - Ask if they should be replaced with the custom endpoint

3. **Replace with custom endpoint** (only if user confirms):
    - Replace with: `https://api.melo.systems/api:VQXw_Ykg/email-form`
    - **IMPORTANT**: Only change the URL in the `action` attribute
    - Do NOT modify any other form attributes, fields, or HTML structure

**Example of What to Look For**:

```html
<!-- BEFORE: Brizy default form -->
<form action="https://msg.formsender.online/form/submit" method="post">
    <input type="text" name="name" />
    <input type="email" name="email" />
    <button type="submit">Submit</button>
</form>
```

**Example of What to Replace It With** (only if user confirms):

```html
<!-- AFTER: Custom endpoint -->
<form action="https://api.melo.systems/api:VQXw_Ykg/email-form" method="post">
    <input type="text" name="name" />
    <input type="email" name="email" />
    <button type="submit">Submit</button>
</form>
```

**Important Notes**:

-   **Always ask the user first** - never replace form URLs without explicit confirmation
-   Only change the URL in the `action` attribute - leave everything else unchanged
-   Search all HTML files, including both default language and translated versions
-   Forms may be embedded in different pages, so check thoroughly
-   If no forms are found, inform the user and skip this step
-   If forms are found but user declines replacement, skip this step and mark as completed

**Search Pattern Examples**:

-   Look for `action="https://msg.formsender.online`
-   Look for `action="https://` followed by form service domains
-   Check for variations in the URL structure

## How to Apply These Modifications

1. Start with a fresh Brizy export
2. Apply modifications sequentially, one at a time:
    - First, restructure the folder organization
    - Second, fix asset paths for files moved to subdirectories
    - Third, fix internal links (remove .html extensions)
    - Fourth, add canonical tags to all pages
    - Fifth, add language tags (hreflang) for multilingual pages
    - Sixth, create sitemap.xml with all pages
    - Seventh, create robots.txt with correct domain
    - Eighth, replace form submission URLs (with user confirmation)
    - Continue with other modifications in order
3. Test thoroughly after each modification
4. Commit changes to Git after each completed step using the step title as the commit message

**Important**: Do not attempt to apply all modifications at once. Complete and test each modification before moving to the next one. This sequential approach makes it easier to identify and fix any issues that may arise.

**Git Commit Messages**: Use the exact title of each modification step as the commit message:

-   "Restructure page organization"
-   "Fix asset paths after restructuring"
-   "Fix internal links (remove .html extensions)"
-   "Add canonical tags"
-   "Add language tags (hreflang)"
-   "Create sitemap.xml"
-   "Create robots.txt"
-   "Replace form submission URLs"

### Modification: Replace Form Submission URLs

**Purpose**: Update form action URLs to point to the correct submission endpoints for the production environment

**Note for CYMTA Project**: The contact forms have been updated to use the correct custom endpoint `action="https://api.melo.systems/api:VQXw_Ykg/email-form"` replacing the previous third-party service URL. Forms now properly submit to the production backend.

**What to Check**:

1. **Form action URLs**: Verify all forms point to correct submission endpoints
2. **External services**: Confirm third-party form services are properly configured
3. **Test submissions**: Verify forms work correctly in production environment

**Common Patterns to Look For**:

-   `action="localhost:3000/submit"` → Update to production URL
-   `action="/api/submit"` → Verify API endpoint exists in production
-   `action="https://formservice.com/submit"` → Verify service is active and configured
