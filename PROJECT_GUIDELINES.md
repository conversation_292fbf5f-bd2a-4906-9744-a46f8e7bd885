# Project Guidelines

These guidelines are intended for AI agents and assistants, as well as human developers working on the project.

## Project Overview

This template serves as a starter project for websites built using the Brizy platform. The workflow is as follows:

1. Website code is initially created and exported from the Brizy platform
2. The exported code is imported into this template (replacing previous versions)
3. We then enhance the website with additional functionality and improvements that are beyond the capabilities of the Brizy platform due to its limitations
4. This approach allows us to leverage Brizy's visual builder while extending its functionality through custom code

### Version Control Workflow

This project uses a straightforward Git workflow to accommodate Brizy's export process:

1. Each time we receive a new export from Brizy, we completely replace the affected files
2. We then re-apply our custom modifications according to the `MODIFICATIONS.md` file
3. All custom modifications are technical fixes that remain consistent across Brizy exports
4. Version history is maintained through Git commits on the main branch

## HTML Formatting Guidelines

### Important: Do Not Modify HTML Formatting

-   **NEVER** reformat or prettify HTML files from Brizy exports
-   Brizy generates HTML with specific class structures and inline styles that are tightly coupled with its rendering engine
-   Even minor formatting changes can break the layout and functionality of Brizy-generated components
-   All HTML files are excluded in `.prettierignore` to prevent accidental formatting
-   When editing HTML, maintain the exact spacing, indentation, and line breaks of the original file
-   If you need to add custom elements, follow the existing formatting pattern precisely

### Working with Brizy HTML

-   Add custom code in dedicated sections when possible, rather than modifying existing Brizy elements
-   When modifying Brizy elements is necessary, make minimal changes and preserve all class names and attributes
-   Test thoroughly after any HTML modifications to ensure the layout renders correctly

## CSS and JavaScript Guidelines

### Prettier Configuration

-   Do NOT format HTML files with Prettier as it breaks the CSS in Brizy platform projects
-   All HTML files are excluded in `.prettierignore` for this reason
-   CSS classes and inline styles in Brizy HTML are tightly coupled with the platform's rendering engine

## Standard Modifications

All standard modifications that need to be applied after each Brizy export are documented in the `MODIFICATIONS.md` file. These modifications address technical limitations of the Brizy platform and remain consistent across different exports.

### AI Agent Trigger Instruction

To trigger the automatic application of Brizy modifications, use this command:

**"Apply Brizy modifications"**

When an AI agent receives this trigger instruction, it should:

1. **Acknowledge and Confirm**: Confirm the trigger and ask for project-specific configuration if not already set in MODIFICATIONS.md
2. **Review Current State**: Check what files exist and their current structure
3. **Follow the Sequential Process**: Work through the MODIFICATIONS.md checklist in order:
    - First: Restructure folder organization
    - Second: Add language/canonical tags
    - Third: Apply any additional modifications in sequence
4. **Test and Verify**: Check that links work and structure is correct after each modification
5. **Update Progress**: Mark completed items in the checklist
6. **Commit Changes**: Make a Git commit after each completed step using the step title as the commit message

**IMPORTANT**: AI agents should commit changes to Git after each modification step is completed. Use the exact title of the modification step as the commit message (e.g., "Restructure page organization", "Fix asset paths after restructuring", etc.).

### AI Agent Action Guidelines

**When AI Agents Should Act**:

-   Only when explicitly prompted with "Apply Brizy modifications" or similar direct instruction
-   When the user specifically requests help with Brizy-related modifications

**When AI Agents Should NOT Act**:

-   Never automatically apply modifications without explicit user instruction
-   Never make assumptions about when modifications are needed
-   Never modify files unless specifically requested by the user

### Git Workflow for Brizy Updates

1. Backup your current project state (optional)
2. Import the new Brizy export files, replacing the existing files
3. Use the trigger instruction "Apply Brizy modifications" or manually re-apply all modifications from the `MODIFICATIONS.md` file
4. Test thoroughly to ensure all modifications work as expected
5. Git commits will be made automatically after each modification step is completed, using the step title as the commit message
